# Rust build artifacts
/target/
/platform/target/
debug/
**/*.rs.bk
*.pdb
Cargo.lock

# Editor and IDE configurations
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.swp
*.swo
.DS_Store

# Xcode related files
*.xcuserstate
xcuserdata/
*.xcworkspace/xcuserdata/
DerivedData/
build/

# Project specific files
*.spv              # Compiled shader files
*.log              # Log files
build_info.rs      # Build script generated files
/vendor/           # Dependencies

# Temporary files
*.tmp
*.bak
.history/
*.vsix
