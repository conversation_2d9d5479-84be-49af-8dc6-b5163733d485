[alias]
# Platform-specific example builds
run-native = [
    "run",
    "--example",
    "moonfield_window",
    "--target-dir",
    "platform/target/native",
]
build-native = [
    "build",
    "--example",
    "moonfield_window",
    "--target-dir",
    "platform/target/native",
]
check-native = ["check", "--target-dir", "platform/target/native"]
clean-native = ["clean", "--target-dir", "platform/target/native"]

generate-xcode = ["xcode", "--manifest-path", "platform/xcode/Cargo.toml", "--output-dir", "platform/xcode"]

[build]
# Default target directory for workspace builds
target-dir = "platform/target/workspace"
