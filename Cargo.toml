[package]
name = "moonfield-examples"
version = "0.1.0"
edition = "2024"

[workspace]
members = [
    "moonfield",
    "moonfield-core",
    "moonfield-graphics", 
    "moonfield-impl",
]
resolver = "3"

# Examples configuration
[[example]]
name = "moonfield_window"
path = "examples/moonfield_window.rs"

[dependencies]
moonfield = { path = "moonfield" }
winit = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[workspace.dependencies]
winit = "0.30.12"
raw-window-handle = "0.6.2"
ash = "0.38.0"
ash-window = "0.13.0"
objc2 = "0.6.1"
objc2-app-kit = "0.3.1"
objc2-core-foundation = "0.3.1"
objc2-metal = "0.3.1"
objc2-quartz-core = "0.3.1"

rand = "0.9.2"

nalgebra = "0.33.2"

# Tracing dependencies
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
tracing-appender = "0.2.3"
tracing-log = "0.2.0"

[profile.dev]
opt-level = 1

[profile.release]
opt-level = 3
debug = true
